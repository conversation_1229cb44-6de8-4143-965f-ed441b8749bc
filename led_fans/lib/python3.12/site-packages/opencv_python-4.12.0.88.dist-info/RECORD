cv2/Error/__init__.pyi,sha256=A6NKtoMeZAvZWHC6DrJiwMVChY7LLxFfvuZ2dW4KSm8,4076
cv2/LICENSE-3RD-PARTY.txt,sha256=a1lmA10WyCsBLhIK6_8zRsjw0-Fa8bxa2ei-kw2IUL8,174432
cv2/LICENSE.txt,sha256=CdcZBY54Kse8cbohyUThE2zeK7lXwOiIEh8CGNa18Cw,1070
cv2/__init__.py,sha256=k2vZTFpd6_AhL8dRr3nToWNlLz6FAlnfIVnbaqPtitg,6612
cv2/__init__.pyi,sha256=tWMJYBiiTHy7v9NGuV1FUvhcecV4uz2eZfMnA-nfbfk,309059
cv2/__pycache__/__init__.cpython-312.pyc,,
cv2/__pycache__/config-3.cpython-312.pyc,,
cv2/__pycache__/config.cpython-312.pyc,,
cv2/__pycache__/load_config_py2.cpython-312.pyc,,
cv2/__pycache__/load_config_py3.cpython-312.pyc,,
cv2/__pycache__/version.cpython-312.pyc,,
cv2/aruco/__init__.pyi,sha256=USi5U17RqL2ByF1UyLF8Apefwb2RxRAdOSCnJcDtgaY,15149
cv2/barcode/__init__.pyi,sha256=19t0bbiTB8nxuT0DyqcTwEWGBynXm6NkaZg646flAL0,1441
cv2/config-3.py,sha256=mnqt9yS4IgAfXpY7Af1ON11F4su-Mo0sp7QqRAwIOhw,724
cv2/config.py,sha256=l04tQJbuGpqaNB3xvzPhaXNoO_GsczAG3if_LyO8WE0,111
cv2/cuda/__init__.pyi,sha256=_-_JAT6uq75LxhpVVnCARGHmjzkUYyfaj68UNU0Ue9A,16205
cv2/cv2.abi3.so,sha256=lBdG4jGHFL4CUqNz8YGHjAZZBaY-K1Hzx4Ry9wMWzZY,66822601
cv2/data/__init__.py,sha256=125Pcte_OtB55ZxjWg5ko8ugpnogZ1sRMyP48dtBCMw,70
cv2/data/__pycache__/__init__.cpython-312.pyc,,
cv2/data/haarcascade_eye.xml,sha256=ccxk_DBaNV3GAGeID2-71D3RVb1j7jhEZhob2jSy_Yw,341406
cv2/data/haarcascade_eye_tree_eyeglasses.xml,sha256=4y-cZ5NcM-nRMx6xT6WFVP8Xg1wDdCZjvLl6iS6Talc,601661
cv2/data/haarcascade_frontalcatface.xml,sha256=rCusk07yQoTviisunY5X7vhKwdaUO00R5cnoWE3Aacg,411388
cv2/data/haarcascade_frontalcatface_extended.xml,sha256=_9DR0o8H0DdsidtMmEUAnChVzHbIz_dj1TMdyTYdqFQ,382918
cv2/data/haarcascade_frontalface_alt.xml,sha256=YoHfE0Wcwhj_BH0Csq44WbEv8UqT_-iVL3sz-te5aXs,676709
cv2/data/haarcascade_frontalface_alt2.xml,sha256=ewyWfZq7373gJeuceGlH0VG2QmBA0HqPlWLtj9kHJLQ,540616
cv2/data/haarcascade_frontalface_alt_tree.xml,sha256=Dl7kfswTJp1U3XpV-LU3UhZ8Ulh3IId3MjiPsHigSAo,2689040
cv2/data/haarcascade_frontalface_default.xml,sha256=D31FJ4ROtRTUpJSOgi2pD7sWo0oLu7xq3GSYdHpar7A,930127
cv2/data/haarcascade_fullbody.xml,sha256=BBdFxx7vG1yGrvIk8XznWwQtMzFMyPZ1dCT4vYzTCqE,476827
cv2/data/haarcascade_lefteye_2splits.xml,sha256=dMMjx4yBR1_JFY-sv7hmuwzKBr5B9XHfR9SsjQH5zkw,195369
cv2/data/haarcascade_license_plate_rus_16stages.xml,sha256=TRxEv3obxOIE-iWwRu0Kz_1_cTzBP-KVi2l3Elxg3eo,47775
cv2/data/haarcascade_lowerbody.xml,sha256=HmluHHxmxDmuIpz_-IcfQgN8NX6eHgkKK1nrwfj_XLs,395322
cv2/data/haarcascade_profileface.xml,sha256=s5pKO-RVOdsUan_B0-dhopLBluuIQhGF5qYVswVeYS0,828514
cv2/data/haarcascade_righteye_2splits.xml,sha256=TPDXK-pzB-mvfrmdSsvhXXEBpnwi_Nz77v1pKtN893Y,196170
cv2/data/haarcascade_russian_plate_number.xml,sha256=gUy1lUaCr1cOWDYfnl-LW1E6QRJ3a7nsrO-fDkymwtc,75482
cv2/data/haarcascade_smile.xml,sha256=TKHzBOq9C1rjAYDIGstT4Walhn5b4Xsxa9PzLP34fYo,188506
cv2/data/haarcascade_upperbody.xml,sha256=cyirT9sVkvU9mNfqWxudkOAa9dlfISrzeMfrV5BIu18,785819
cv2/detail/__init__.pyi,sha256=FXndW6oxsE46hjgKBezLvqJ_iEAcOCmNOAZSpbSM_-8,22374
cv2/dnn/__init__.pyi,sha256=ErtfRy7PMtICL2EMdHGE5EsQZkURWchTNywNNrPKR-8,23111
cv2/fisheye/__init__.pyi,sha256=RUsWW7grpTh-YezBpGQSPpgIihGFp5cC0J3FN5WSVic,9931
cv2/flann/__init__.pyi,sha256=ZxYG07bhFyFRA2d1lbPmAm_KEknsTcE1_NNw_Ksz1HQ,2677
cv2/gapi/__init__.py,sha256=6WBAjfq1FCiRADgYXGAKITHdBB6t0_jZ8hkTU8Biz-M,10298
cv2/gapi/__init__.pyi,sha256=zCLTsHvmbiGmlDUXPWqOGdgFcj66_iw7FXiTr4Y91m0,14636
cv2/gapi/__pycache__/__init__.cpython-312.pyc,,
cv2/gapi/core/__init__.pyi,sha256=_3OM_ITOrZomn7gs4HM-DRk8ngbjWkdr26KrmH3t4ks,142
cv2/gapi/core/cpu/__init__.pyi,sha256=MfRTDEPtcQekGnrvoaSSadxyylXPfa2lz8ucAkzjmh8,93
cv2/gapi/core/fluid/__init__.pyi,sha256=MfRTDEPtcQekGnrvoaSSadxyylXPfa2lz8ucAkzjmh8,93
cv2/gapi/core/ocl/__init__.pyi,sha256=MfRTDEPtcQekGnrvoaSSadxyylXPfa2lz8ucAkzjmh8,93
cv2/gapi/ie/__init__.pyi,sha256=rbOXOU39Wpt9Lhh1o1qr7Zj7qljqAu6aqoYsm4433yQ,1117
cv2/gapi/ie/detail/__init__.pyi,sha256=hGTS3yIiIq1B-djXgSQIPmeF7VDyeyucUuZOnd4O0OQ,269
cv2/gapi/imgproc/__init__.pyi,sha256=UUtPJcDK_UaE_TKN8K9Oz1TEChCQHDDB_eTI08mVXmU,71
cv2/gapi/imgproc/fluid/__init__.pyi,sha256=MfRTDEPtcQekGnrvoaSSadxyylXPfa2lz8ucAkzjmh8,93
cv2/gapi/oak/__init__.pyi,sha256=Tb7YXytKxnBFZZ8qTqHSZsDEpRt2937NXtbOQK23Ksc,1734
cv2/gapi/onnx/__init__.pyi,sha256=aDuvbrUkpNnxr-V0EGQ9hQ34YekwWFFx_QuK1fq6ROk,1497
cv2/gapi/onnx/ep/__init__.pyi,sha256=dUYUbcjIjWtx7peQLPKU60qUzMqEH8On9mU4lsdXbmQ,1357
cv2/gapi/ot/__init__.pyi,sha256=XTMT90lnElxl_KfhFi5xDwQWvB0g5N8tf7Cgb8VHcAY,720
cv2/gapi/ot/cpu/__init__.pyi,sha256=MfRTDEPtcQekGnrvoaSSadxyylXPfa2lz8ucAkzjmh8,93
cv2/gapi/ov/__init__.pyi,sha256=3BqKzC_lV-wzhwu2cawCBvGbMG_zxt5D6anjhORXvuM,2647
cv2/gapi/own/__init__.pyi,sha256=GzL91pOQQNsGcBGmZ_XDAXaLoF4N9qVgj_IaYzduSNc,69
cv2/gapi/own/detail/__init__.pyi,sha256=sTC8JFcjDcVxnaFfFc-VmuxjHBg6RMzfafFHtS8yrFU,140
cv2/gapi/render/__init__.pyi,sha256=S4FWzy_CJqqs3dPYl3bXJoLQSGeVZdoBK7EmHvbPVOM,66
cv2/gapi/render/ocv/__init__.pyi,sha256=MfRTDEPtcQekGnrvoaSSadxyylXPfa2lz8ucAkzjmh8,93
cv2/gapi/streaming/__init__.pyi,sha256=qIOndKlPMevrSglTW-vVugzy_n7nITT6lr_zrlUv9cI,813
cv2/gapi/video/__init__.pyi,sha256=V0Emspufw7x2-knfd7kE8LnLjY_ujIz_TaxR_oIyAps,150
cv2/gapi/wip/__init__.pyi,sha256=f7mz60ehM9yrK0_Vt28NP--WietDE65EjM5O91LVx5M,1086
cv2/gapi/wip/draw/__init__.pyi,sha256=x2BhywI5C-uMHF1H6L9AwrgjRtKHFr032TOnqtE9a9Q,3162
cv2/gapi/wip/gst/__init__.pyi,sha256=8VtSKP9duTmY7ETAACwzVEWP9xdDW0pW82UtL_8Z7Aw,467
cv2/gapi/wip/onevpl/__init__.pyi,sha256=eLbVPey7JCU5YdRSUH6lLlD1eT-1s7YqZrQh6xNdIlo,397
cv2/ipp/__init__.pyi,sha256=WSHVIqIT97vmudtuJjhOJYiZ0iBdYx4AtB0iJqtdD0o,223
cv2/load_config_py2.py,sha256=xP_h2pObzfbN8tONV7CAQmGh94fQ-0t0HysrXDDlt_Q,151
cv2/load_config_py3.py,sha256=A9wfETdKZnybfbEN1SdtZAsMLVsueGa0zO93JzK9OFI,262
cv2/mat_wrapper/__init__.py,sha256=i2JwY6kmDL_s7YXzIl-JZuWCMVYkRi4F6j60W3j4P9A,1124
cv2/mat_wrapper/__pycache__/__init__.cpython-312.pyc,,
cv2/misc/__init__.py,sha256=yr9PkxKslxRc87hhtIJRn5RommP9jaqksYr-ZDuj7cU,37
cv2/misc/__pycache__/__init__.cpython-312.pyc,,
cv2/misc/__pycache__/version.cpython-312.pyc,,
cv2/misc/version.py,sha256=iTExq1jwGgAv3jtYQHRI8pSpmfzPsjkG9brsH0bdYhk,90
cv2/ml/__init__.pyi,sha256=KGiSrNBU8YWqJzhV3owS_b_nKl_40EXwdGrmC1e41J4,22803
cv2/ocl/__init__.pyi,sha256=qv_ilpHZosfPEMHEEqqQLe6cJpsb9PiiwIZMbd---ho,5527
cv2/ogl/__init__.pyi,sha256=KxTX9DHYyXg2ipvOJiFeAsRivAjmvBkqeiLZV-0snII,1472
cv2/parallel/__init__.pyi,sha256=tc5nNoWrTkD7VAfhbajumKF79LBolpqlKjYX-lY2__8,129
cv2/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
cv2/qt/fonts/DejaVuSans-Bold.ttf,sha256=5JIhu_F8A2EnQUMWn35vFthxX2XUnx2fIW6z1mFAAwg,672300
cv2/qt/fonts/DejaVuSans-BoldOblique.ttf,sha256=c1891zgbb04MpRm3JiDHWqlT7o7om4jL5POMo8I9ank,611212
cv2/qt/fonts/DejaVuSans-ExtraLight.ttf,sha256=kI1uyALygVXI3oYZK1p3qftBeS8HLgNSbwU2wjTz6aA,345204
cv2/qt/fonts/DejaVuSans-Oblique.ttf,sha256=O8nAL-_K3VF-WhWLLzQjPdNU1n9DAkhtiOhLykZ9HUM,611556
cv2/qt/fonts/DejaVuSans.ttf,sha256=FdotjxLmlQABscyCJcG6ct3OGTiDfTdwL_Ppv215vV4,720012
cv2/qt/fonts/DejaVuSansCondensed-Bold.ttf,sha256=-GxtQKUv_itA8Z0Llyykuc40f8BNz8TQtOkneocSwN0,631992
cv2/qt/fonts/DejaVuSansCondensed-BoldOblique.ttf,sha256=ZldpOhjs7-4mZ9nQ7LGrtoUk1ieZm9g2UDnBngS0I4E,580168
cv2/qt/fonts/DejaVuSansCondensed-Oblique.ttf,sha256=SPmU6BKEZmq0v4nvTXMIWwf65sLH4oggqyQ-mUHEgp4,576004
cv2/qt/fonts/DejaVuSansCondensed.ttf,sha256=afE1XJ7vCj0RpsBvPL8dRuq_2tzJk1iaO-k6RO2GeLQ,643852
cv2/qt/plugins/platforms/libqxcb.so,sha256=5xLPjOeVf0IRbSEYlBQMeRBo2jb1QWK492t9ybX7seQ,29313
cv2/samples/__init__.pyi,sha256=cjSW5vo2oMpIWHwP-3IY4hWjlKUTz8gd1MX7pLOCWKo,324
cv2/segmentation/__init__.pyi,sha256=jwKBUCRaXhHAM3FdzpLuGucGfNLWxWu5CDfLOpkcan4,1739
cv2/typing/__init__.py,sha256=T0byS9BWHpUVAy-TeMvOOr7w1An0cXb7UjSl6zneLAA,5365
cv2/typing/__pycache__/__init__.cpython-312.pyc,,
cv2/utils/__init__.py,sha256=fuw4GHHOXsxxKc-AadAEOKQq_I1Gr4G3yMlRvAbTP30,330
cv2/utils/__init__.pyi,sha256=q7PpnVUH597R_sF7AGrsRVDOIGKflT0b77ll-mkmb7g,3592
cv2/utils/__pycache__/__init__.cpython-312.pyc,,
cv2/utils/fs/__init__.pyi,sha256=lu2cK1Dbd7wRTOTju_kVVCvU4mNB5v5hSVpBxSXXvJg,87
cv2/utils/nested/__init__.pyi,sha256=n2J3aSxC2MrPKaKb4igY_d49luuuQqW7A_YTx6eZz9Q,573
cv2/version.py,sha256=Z5AB6EB8NKiMcFYzT9AgjGAdwZ9-HGJQSt0WSntf02Y,93
cv2/videoio_registry/__init__.pyi,sha256=ef4uptXuTkAZVXwsyNSc-Xhs-hJlg6O2AcmoYkzEx80,962
opencv_python-4.12.0.88.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
opencv_python-4.12.0.88.dist-info/LICENSE-3RD-PARTY.txt,sha256=a1lmA10WyCsBLhIK6_8zRsjw0-Fa8bxa2ei-kw2IUL8,174432
opencv_python-4.12.0.88.dist-info/LICENSE.txt,sha256=CdcZBY54Kse8cbohyUThE2zeK7lXwOiIEh8CGNa18Cw,1070
opencv_python-4.12.0.88.dist-info/METADATA,sha256=UYg3zY6T-KUuz9BeMEyxnZP5PAHfmm5KsIdNoEFBudc,19699
opencv_python-4.12.0.88.dist-info/RECORD,,
opencv_python-4.12.0.88.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
opencv_python-4.12.0.88.dist-info/WHEEL,sha256=z9ZxY63jT7lbO94x_nkDqPBptV6C1ib4bMJb1l6mQWE,142
opencv_python-4.12.0.88.dist-info/top_level.txt,sha256=SY8vrf_sYOg99OP9euhz7q36pPy_2VK5vbeEWXwwSoc,4
opencv_python.libs/libQt5Core-e7f476e2.so.5.15.16,sha256=2tgxpg6UAZOhgXlBTEZt1CQu9AkeVlakvnNGkxAFfzY,7424289
opencv_python.libs/libQt5Gui-3e966859.so.5.15.16,sha256=dVzs2CnOJB-08iWcMdNgv3kP3K4YnTEQ78K9ogps0Pg,9565337
opencv_python.libs/libQt5Test-9ac3ed15.so.5.15.16,sha256=Wkw51CAhhX_m3TcA-q5J2cmwIBdhy68j6KbJP8k-8pU,424057
opencv_python.libs/libQt5Widgets-cd430389.so.5.15.16,sha256=bVTIeP581U7HEpX7aJvWuIUZsqpT10bz6fsePi7sjWs,8926585
opencv_python.libs/libQt5XcbQpa-3cfa6167.so.5.15.16,sha256=sw9yK9Er6sXgdn9cu6ZcCdeY_rCI1B_Y1NatFHXJ5q4,1854273
opencv_python.libs/libX11-xcb-0e257303.so.1.0.0,sha256=qM9S9n-2hEv6GdR_37Z4GgKvPKav54up-1ijFLk2XXI,8873
opencv_python.libs/libXau-00ec42fe.so.6.0.0,sha256=JjysEtjYterX3CORw1X-n8k5lA4eoi7ZjuVqjLYc5oQ,17049
opencv_python.libs/libaom-49d00b71.so.3.12.1,sha256=aV96HjcMUQMWtKVpIEMgt0ePrWxs42I0NOzy57YGNHk,7506401
opencv_python.libs/libavcodec-e0dd92b8.so.59.37.100,sha256=YVp1jyPtJVxm97ZFwX82HBsTcOtTjqzRORF3EQlkMXY,13452617
opencv_python.libs/libavformat-d296e685.so.59.27.100,sha256=RGQOVVE2AJYksu1xew30TJ0qOewqnGltIK9_PZXR97A,2571489
opencv_python.libs/libavif-850e7649.so.16.3.0,sha256=1EMK1JejW_JhYFLnNv5YwxUSl6-OLS9sudNvOeE5oYg,1074561
opencv_python.libs/libavutil-734d06dd.so.57.28.100,sha256=sgfG0I9y57wEfj0X-sGp8jxhBZ_9MMuoBASJ6K15rjM,844673
opencv_python.libs/libcrypto-01067bc0.so.1.1,sha256=kxtHGNMWWi6G2CctiY8QMlkLd0qLa3jleKJDxsjYwBw,3481345
opencv_python.libs/libgfortran-91cc3cb1.so.3.0.0,sha256=VePrZzBsL_F-b4oIEOqg3LJulM2DkkxQZdUEDoeBRgg,1259665
opencv_python.libs/libopenblas-r0-f650aae0.3.3.so,sha256=eewCtT9XPNcRaonwTDl0cwGOf9oFcgs1TUNQXBnUeVg,37325001
opencv_python.libs/libpng16-04239421.so.16.48.0,sha256=ua3jwLmJL2P_ezaut6EHFr57Dh2nDmEuTNqNykUj91c,1093105
opencv_python.libs/libquadmath-96973f99.so.0.0.0,sha256=k0wi3tDn0WnE1GeIdslgUa3z2UVF2pYvYLQWWbB12js,247609
opencv_python.libs/libssl-28bef1ac.so.1.1,sha256=IkBOoNPuWstb1igPKVZOOpwq1AvAmV7fKzMQQQweLMk,736177
opencv_python.libs/libswresample-3e7db482.so.4.7.100,sha256=4YcABnk9g5akqnpts-IoqgowrkeBpjl4e8HcePdrwiA,132417
opencv_python.libs/libswscale-95ddd674.so.6.7.100,sha256=chVatRSRMGIfJEqcIiWXEyCPJeClspWVdHm507wYW-E,619945
opencv_python.libs/libvpx-127417df.so.11.0.0,sha256=XawQOtzCuH5bbUkgdaWaZz0ModyMOUcS1KRCjUikXPg,3524625
opencv_python.libs/libxcb-icccm-413c9f41.so.4.0.0,sha256=KrtUIHu46x9mIwMEkEYflhOFmYFjvUB3Ok1Dn9936eI,24377
opencv_python.libs/libxcb-image-e82a276d.so.0.0.0,sha256=QYC_KsToCXKQ2u87uOb2WJmK6Z-S4yynjqYWiI3stTY,25601
opencv_python.libs/libxcb-keysyms-21015570.so.1.0.0,sha256=PjX3WLcXNZucKONqtqBW4wPbmcaukPVyLPu2JCXZ7QQ,13209
opencv_python.libs/libxcb-randr-a96a5a87.so.0.1.0,sha256=LZmVHqS5soTrAUfIJ4cy0BKHrBk0Q8cy7IBJFbhsHvY,93921
opencv_python.libs/libxcb-render-637b984a.so.0.0.0,sha256=COOiubLk9Kv2S4wVA5QaRzgllJYpLLGXjYQAKM3hs2c,78105
opencv_python.libs/libxcb-render-util-43ce00f5.so.0.0.0,sha256=N0OPbas7C-jZx7kb3--foJiJPc5odPSj-hdma1yRG2E,22161
opencv_python.libs/libxcb-shape-25c2b258.so.0.0.0,sha256=8xHTe9DQmFzk-5HtT33th8bvgCroLJiEvXdAiN3i1io,21769
opencv_python.libs/libxcb-shm-7a199f70.so.0.0.0,sha256=XrF9nlIKkNrLG9HkXnn_XIeIHPwr20hRrTWETbzVGwE,21377
opencv_python.libs/libxcb-sync-89374f40.so.1.0.0,sha256=-w1wV0pfEQbSmW-QGzsRSADRNReahcQtlYgqIjKgHeE,35673
opencv_python.libs/libxcb-util-4d666913.so.1.0.0,sha256=44mg7PRdg-AK2vHz0GT1yzW0iN8d_GUFvhFGlrLtMo8,26281
opencv_python.libs/libxcb-xfixes-9be3ba6f.so.0.0.0,sha256=n5_94_1LwyIvg9S1I1dbu6a3ROBn28MQgT-maLnRtFM,45337
opencv_python.libs/libxcb-xinerama-ae147f87.so.0.0.0,sha256=iUXAB0Ox6t7vVAJOQEzTK4GVjW3AbnHOFsWyxml6RNo,17529
opencv_python.libs/libxcb-xkb-9ba31ab3.so.1.0.0,sha256=4toATK-D72nN4FjDv7ZCXjkMpU1Giroj5hr2ebVlOjk,157921
opencv_python.libs/libxkbcommon-71ae2972.so.0.0.0,sha256=H8s4pka9HOHar2gq0pty5lv99noGM1snj46Z0LdTAhI,269865
opencv_python.libs/libxkbcommon-x11-c65ed502.so.0.0.0,sha256=NLByawCP4Fm3AgQmUIDl2zSvrMCvKhJpitbDiuEWbVQ,48097
